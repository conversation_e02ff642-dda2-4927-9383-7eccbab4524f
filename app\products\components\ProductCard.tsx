"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { ArrowRight, Star, TrendingUp, Users, Shield, Zap } from "lucide-react"
import { cn } from "@/lib/utils"
import { motion } from "framer-motion"
import {
  Brain,
  Cpu,
  GraduationCap,
  Database,
  Eye,
  Target,
  CheckCircle2,
  Globe,
  BarChart,
  BookOpen,
  Award,
  Settings,
  type LucideIcon
} from "lucide-react"




interface ProductFeature {
  text: string
  iconName: string
}

interface Product {
  slug: string
  name: string
  description: string
  iconName: string
  features: ProductFeature[]
  highlight?: string
  price?: string
  category?: string
}

interface ProductCardProps {
  product: Product
  index: number
}

// 图标映射
const ICON_MAP: Record<string, LucideIcon> = {
  Brain: Brain,
  Cpu: Cpu,
  GraduationCap: GraduationCap,
  Database: Database,
  Eye: Eye,
  Target: Target,
  CheckCircle2: CheckCircle2,
  Globe: Globe,
  BarChart: BarChart,
  BookOpen: BookOpen,
  Award: Award,
  Settings: Settings,
  Zap: Zap,
  Shield: Shield,
  Users: Users
}

// 渲染图标函数
const renderIcon = (iconName: string, className?: string) => {
  const IconComponent = ICON_MAP[iconName]
  if (!IconComponent) {
    return <div className={cn("bg-blue-500/20 rounded", className)} />
  }
  return <IconComponent className={className} />
}

export function ProductCard({ product, index }: ProductCardProps) {
  // 根据不同产品类型设置不同的颜色主题
  const getThemeColors = (category: string) => {
    switch (category) {
      case 'AI服务':
        return {
          primary: 'from-blue-500 to-blue-600',
          secondary: 'from-blue-50 to-indigo-50',
          accent: 'rgb(59 130 246)',
          light: 'rgba(59, 130, 246, 0.1)'
        }
      case '云计算':
        return {
          primary: 'from-green-500 to-green-600',
          secondary: 'from-green-50 to-emerald-50',
          accent: 'rgb(34 197 94)',
          light: 'rgba(34, 197, 94, 0.1)'
        }
      case '教育科技':
        return {
          primary: 'from-purple-500 to-purple-600',
          secondary: 'from-purple-50 to-violet-50',
          accent: 'rgb(168 85 247)',
          light: 'rgba(168, 85, 247, 0.1)'
        }
      default:
        return {
          primary: 'from-blue-500 to-blue-600',
          secondary: 'from-blue-50 to-indigo-50',
          accent: 'rgb(59 130 246)',
          light: 'rgba(59, 130, 246, 0.1)'
        }
    }
  }

  const theme = getThemeColors(product.category || '')

  return (
    <motion.div
      className="group relative h-full"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true }}
      whileHover={{ y: -8 }}
    >
      <div className={cn(
        "relative overflow-hidden rounded-3xl bg-white border-2 border-transparent h-full flex flex-col",
        "shadow-lg hover:shadow-2xl transition-all duration-500",
        "before:absolute before:inset-0 before:rounded-3xl before:p-[2px]",
        `before:bg-gradient-to-br ${theme.primary}`,
        "before:mask-composite:exclude before:[mask:linear-gradient(#fff_0_0)_content-box,linear-gradient(#fff_0_0)]",
        product.highlight && "ring-2 ring-offset-4 ring-blue-200"
      )}>
        {/* 背景渐变 */}
        <div className={`absolute inset-0 bg-gradient-to-br ${theme.secondary} opacity-50`} />

        {/* 高亮标签 */}
        {product.highlight && (
          <motion.div
            className={`absolute -top-2 -right-2 px-4 py-2 text-xs font-bold text-white rounded-full shadow-lg bg-gradient-to-r ${theme.primary} z-10`}
            animate={{ scale: [1, 1.05, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            {product.highlight}
          </motion.div>
        )}

        {/* 装饰性图案 */}
        <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
          <div className={`w-full h-full rounded-full bg-gradient-to-br ${theme.primary} transform translate-x-16 -translate-y-16`} />
        </div>

        <div className="relative p-8 flex flex-col h-full">
          {/* 头部区域 */}
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-center gap-4">
              {/* 主图标 */}
              <div className="relative">
                <motion.div
                  className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${theme.primary} flex items-center justify-center shadow-lg`}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ duration: 0.3 }}
                >
                  {renderIcon(product.iconName, "h-8 w-8 text-white")}
                </motion.div>
                <div className="absolute inset-0 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ background: theme.light }} />
              </div>
            </div>

            {/* 分类标签 */}
            {product.category && (
              <span className={`px-3 py-1.5 text-xs font-semibold rounded-full bg-white/80 backdrop-blur-sm border`} style={{ color: theme.accent, borderColor: theme.light }}>
                {product.category}
              </span>
            )}
          </div>

          {/* 标题和描述 */}
          <div className="mb-6">
            <h3 className="text-xl font-bold text-slate-800 mb-3 group-hover:text-gradient-modern transition-all duration-300">
              {product.name}
            </h3>
            <p className="text-sm text-slate-600 leading-relaxed line-clamp-3">
              {product.description}
            </p>
          </div>

          {/* 特性网格 */}
          <div className="grid grid-cols-2 gap-3 mb-6 flex-grow">
            {product.features.slice(0, 4).map((feature, featureIndex) => (
              <motion.div
                key={feature.text}
                className="flex items-center gap-2 p-3 rounded-xl bg-white/60 backdrop-blur-sm border border-white/20 group/feature hover:bg-white/80 transition-all duration-200"
                whileHover={{ scale: 1.02 }}
                initial={{ opacity: 0, x: -10 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: featureIndex * 0.1 }}
                viewport={{ once: true }}
              >
                <div className={`w-6 h-6 rounded-lg bg-gradient-to-br ${theme.primary} flex items-center justify-center flex-shrink-0`}>
                  {renderIcon(feature.iconName, "h-3 w-3 text-white")}
                </div>
                <span className="text-xs font-medium text-slate-700 group-hover/feature:text-slate-900 transition-colors">
                  {feature.text}
                </span>
              </motion.div>
            ))}
          </div>

          {/* 统计信息 */}
          <div className="grid grid-cols-3 gap-4 mb-6 p-4 rounded-xl bg-white/40 backdrop-blur-sm border border-white/20">
            <div className="text-center">
              <div className="text-lg font-bold" style={{ color: theme.accent }}>99%</div>
              <div className="text-xs text-slate-500">满意度</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold" style={{ color: theme.accent }}>24/7</div>
              <div className="text-xs text-slate-500">支持</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold" style={{ color: theme.accent }}>
                <Star className="h-4 w-4 inline fill-current" />
                4.9
              </div>
              <div className="text-xs text-slate-500">评分</div>
            </div>
          </div>

          {/* 价格信息 */}
          {product.price && (
            <div className="mb-6 p-4 rounded-xl bg-white/60 backdrop-blur-sm border border-white/20">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-xs text-slate-500 mb-1">起始价格</div>
                  <div className="text-lg font-bold" style={{ color: theme.accent }}>
                    {product.price}
                  </div>
                </div>
                <TrendingUp className="h-5 w-5 text-green-500" />
              </div>
            </div>
          )}

          {/* 按钮 */}
          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button
              asChild
              className={cn(
                "w-full mt-auto group/btn relative overflow-hidden",
                product.highlight
                  ? "text-white shadow-lg"
                  : "bg-white border-2 hover:bg-white hover:shadow-lg"
              )}
              style={{
                background: product.highlight
                  ? `linear-gradient(135deg, ${theme.primary.replace('from-', '').replace(' to-', ', ')})`
                  : 'white',
                borderColor: product.highlight ? 'transparent' : theme.light,
                color: product.highlight ? 'white' : theme.accent
              }}
            >
              <Link href={`/products/${product.slug}`} className="flex items-center justify-center gap-2 py-3">
                <span className="font-semibold">了解详情</span>
                <ArrowRight className="h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
              </Link>
            </Button>
          </motion.div>
        </div>

        {/* 悬停效果 */}
        <div className={`absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 bg-gradient-to-br ${theme.primary} mix-blend-overlay`} style={{ opacity: 0.05 }} />
      </div>
    </motion.div>
  )
}
