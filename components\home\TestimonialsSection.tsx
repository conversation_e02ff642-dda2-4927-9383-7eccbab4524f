import Image from 'next/image'
import React from 'react'
import { motion } from "framer-motion"

function TestimonialsSection() {
  const testimonials = [
    {
      content: "0dot的AI智能标注服务大大提升了我们的数据处理效率，标注精度高达99.5%，为我们的机器学习项目节省了大量时间和成本。",
      author: "张明",
      role: "AI研发总监",
      company: "智能科技有限公司",
      image: "https://picsum.photos/seed/user1/200/200"
    },
    {
      content: "使用0dot的CPU租用服务进行深度学习训练，性能稳定，价格合理。弹性扩容功能让我们能够根据项目需求灵活调整资源。",
      author: "李华",
      role: "技术架构师",
      company: "数据科学研究院",
      image: "https://picsum.photos/seed/user2/200/200"
    },
    {
      content: "教育培训管理系统功能全面，界面友好。在线考试系统特别出色，支持多种题型，防作弊功能强大，大大提升了我们的教学管理效率。",
      author: "王芳",
      role: "教务主任",
      company: "现代教育集团",
      image: "https://picsum.photos/seed/user3/200/200"
    }
  ]

  return (
    <div className="py-24 sm:py-32 bg-slate-50/50">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-xl text-center">
          <h2 className="text-lg font-semibold leading-8" style={{ color: 'rgb(59 130 246)' }}>用户反馈</h2>
          <p className="mt-2 text-3xl font-bold tracking-tight sm:text-4xl text-gradient-modern">
            值得信赖的技术伙伴
          </p>
        </div>
        <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-3">
          {testimonials.map((testimonial, index) => (
            <motion.figure
              key={testimonial.author}
              className="card-modern p-8 group hover-glow"
              style={{ borderColor: 'rgba(59, 130, 246, 0.1)' }}
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <div className="relative">
                <div className="absolute -top-2 -left-2 text-6xl font-serif" style={{ color: 'rgba(59, 130, 246, 0.2)' }}>"</div>
                <blockquote className="text-lg font-medium leading-relaxed relative z-10 mb-6">
                  <p className="text-slate-700">
                    {testimonial.content}
                  </p>
                </blockquote>
              </div>
              <figcaption className="flex items-center gap-x-4">
                <div className="relative">
                  <Image
                    className="h-12 w-12 rounded-full ring-2 ring-blue-200 group-hover:ring-blue-400 transition-all duration-300"
                    src={testimonial.image}
                    alt=""
                    width={48}
                    height={48}
                  />
                  <div className="absolute inset-0 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ background: 'rgba(59, 130, 246, 0.1)' }} />
                </div>
                <div>
                  <div className="font-semibold text-slate-800">
                    {testimonial.author}
                  </div>
                  <div className="text-sm text-slate-500">
                    {testimonial.role}，{testimonial.company}
                  </div>
                </div>
              </figcaption>
              <div className="absolute bottom-0 left-0 right-0 h-1 rounded-b-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ background: 'linear-gradient(90deg, rgb(59 130 246), rgb(37 99 235), rgb(29 78 216))' }} />
            </motion.figure>
          ))}
        </div>
      </div>
    </div>
  )
}

export default TestimonialsSection