'use client'

import React from 'react'
import { Brain, Cpu, GraduationCap } from 'lucide-react'
import { motion } from "framer-motion"

function FeaturesSection() {
  const features = [
    {
      name: 'AI智能标注',
      description: '提供高精度的数据标注服务，支持图像、文本、语音等多模态数据，为机器学习模型训练提供优质数据集。',
      icon: Brain,
      details: ['图像标注', '文本标注', '语音标注', '视频标注', '3D点云标注']
    },
    {
      name: 'CPU算力租用',
      description: '灵活的云计算资源租用服务，提供高性能CPU集群，支持科学计算、深度学习训练等高算力需求场景。',
      icon: Cpu,
      details: ['高性能计算', '弹性扩容', '按需付费', '24/7监控', '数据安全']
    },
    {
      name: '教育培训管理',
      description: '一站式教育培训管理平台，涵盖课程管理、学员管理、考试系统、证书颁发等完整教育生态链。',
      icon: GraduationCap,
      details: ['课程管理', '在线考试', '学员跟踪', '证书系统', '数据分析']
    }
  ]

  return (
    <section className="py-32 sm:py-40 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-b from-blue-50/40 via-indigo-50/20 to-slate-50" />
      <div className="absolute top-0 left-1/4 w-96 h-96 rounded-full blur-3xl" style={{ background: 'rgba(59, 130, 246, 0.06)' }} />
      <div className="absolute bottom-0 right-1/4 w-96 h-96 rounded-full blur-3xl" style={{ background: 'rgba(37, 99, 235, 0.04)' }} />

      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        <motion.div
          className="mx-auto max-w-3xl text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-lg font-semibold leading-7 mb-4" style={{ color: 'rgb(59 130 246)' }}>技术创新</h2>
          <p className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl text-gradient-modern mb-8">
            引领行业数字化转型
          </p>
          <p className="text-xl leading-relaxed text-slate-600">
            秉持"技术引领未来，创新驱动发展"的理念，我们致力于将前沿科技转化为实际应用，为客户创造价值。
          </p>
        </motion.div>

        <div className="mx-auto mt-20 max-w-2xl sm:mt-24 lg:mt-32 lg:max-w-none">
          <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-20 lg:max-w-none lg:grid-cols-3">
            {features.map((feature, index) => (
              <motion.div
                key={feature.name}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
              >
                <FeatureCard feature={feature} />
              </motion.div>
            ))}
          </dl>
        </div>
      </div>
    </section>
  )
}

export default FeaturesSection


// Feature Card Component
function FeatureCard({ feature }: { feature: any }) {
  return (
    <div className="relative group h-full">
      <div className="card-modern p-8 h-full hover-glow" style={{ borderColor: 'rgba(59, 130, 246, 0.1)' }}>
        <div className="flex items-center gap-4 mb-6">
          <div className="relative">
            <div className="flex h-16 w-16 items-center justify-center rounded-2xl shadow-lg" style={{ background: 'linear-gradient(135deg, rgb(59 130 246), rgb(37 99 235))' }}>
              <feature.icon className="h-8 w-8 text-white" aria-hidden="true" />
            </div>
            <div className="absolute inset-0 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ background: 'rgba(59, 130, 246, 0.2)' }} />
          </div>
          <div>
            <h3 className="text-xl font-bold text-slate-800 transition-colors duration-300 group-hover:text-gradient-modern">
              {feature.name}
            </h3>
          </div>
        </div>
        <p className="text-slate-600 leading-relaxed mb-6 flex-grow">
          {feature.description}
        </p>
        <div className="flex flex-wrap gap-2">
          {feature.details.map((detail: string, index: number) => (
            <span
              key={detail}
              className="inline-flex items-center rounded-full px-3 py-1.5 text-sm font-medium transition-all duration-200 hover-lift"
              style={{
                backgroundColor: 'rgba(59, 130, 246, 0.08)',
                color: 'rgb(59 130 246)',
                animationDelay: `${index * 100}ms`
              }}
            >
              {detail}
            </span>
          ))}
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-1 rounded-b-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ background: 'linear-gradient(90deg, rgb(59 130 246), rgb(37 99 235), rgb(29 78 216))' }} />
      </div>
    </div>
  )
}