"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Check } from "lucide-react"
import Link from "next/link"
import {
  Layers,
  Shield,
  Zap,
  Settings,
  Users,
  BarChart,
  Cloud,
  Lock,
  Rocket,
  Code,
  LineChart,
  Database,
  Network,
  Server,
  Cpu,
  Target,
  Eye,
  MapPin,
  Tag,
  Heart,
  TrendingUp,
  Monitor,
  DollarSign,
  BookOpen,
  FileText,
  FileCheck,
  Award,
  Calendar,
  Scissors,
  type LucideIcon
} from "lucide-react"
import Image from "next/image"
import { useState } from "react"
import { Play } from "lucide-react"
import { Loader2 } from "lucide-react"

interface Benefit {
  title: string;
  description: string;
}

interface Feature {
  name: string;
  description: string;
  icon?: string;
}

interface FeatureCategory {
  title: string;
  description?: string;
  features: Feature[];
}

interface Product {
  name: string;
  description: string;
  features: string[];
  demoVideo?: {
    url: string;
    thumbnail?: string;
  };
  benefits?: Benefit[];
  featureList?: FeatureCategory[];
}

interface ProductDetailProps {
  product: Product;
}

const ICON_MAP: Record<string, LucideIcon> = {
  Layers: Layers,
  Shield: Shield,
  Zap: Zap,
  Settings: Settings,
  Users: Users,
  BarChart: BarChart,
  Cloud: Cloud,
  Lock: Lock,
  Rocket: Rocket,
  Code: Code,
  LineChart: LineChart,
  Database: Database,
  Network: Network,
  Server: Server,
  Cpu: Cpu,
  Target: Target,
  Eye: Eye,
  MapPin: MapPin,
  Tag: Tag,
  Heart: Heart,
  TrendingUp: TrendingUp,
  Monitor: Monitor,
  DollarSign: DollarSign,
  BookOpen: BookOpen,
  FileText: FileText,
  FileCheck: FileCheck,
  Award: Award,
  Calendar: Calendar,
  Scissors: Scissors
}

export function ProductDetail({ product }: ProductDetailProps) {
  const [isPlaying, setIsPlaying] = useState(false)

  const renderIcon = (iconName?: string) => {
    if (!iconName) return null

    const IconComponent = ICON_MAP[iconName]
    if (!IconComponent) {
      console.warn(`Icon ${iconName} not found in ICON_MAP`)
      return <div className="w-6 h-6 bg-primary/20 rounded" />
    }

    return (
      <IconComponent
        className="w-6 h-6 text-primary group-hover:scale-110 transition-transform"
      />
    )
  }

  return (
    <div className="py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        {/* 产品标题和描述 */}
        <div className="mx-auto max-w-2xl sm:text-center">
          <h1 className="text-3xl font-bold tracking-tight sm:text-4xl bg-gradient-to-r from-primary to-primary/50 bg-clip-text text-transparent">
            {product.name}
          </h1>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            {product.description}
          </p>
        </div>

        {/* 主要特性部分 - 现在占据全宽 */}
        <div className="mx-auto mt-16 max-w-4xl">
          <h3 className="text-2xl font-bold tracking-tight mb-8">主要特性</h3>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {product.features.map((feature, index) => (
              <div key={index} className="flex items-center gap-x-3 p-4 rounded-lg hover:bg-muted/50 transition-colors">
                <Check className="h-6 w-5 flex-shrink-0 text-primary" />
                <span className="text-sm leading-6">{feature}</span>
              </div>
            ))}
          </div>
        </div>

        {/* 产品介绍视频部分 - 在主要特性之后添加 */}
        {product.demoVideo && (
          <div className="mt-24">
            <h3 className="text-2xl font-bold tracking-tight mb-8 text-center">产品演示</h3>
            <div className="max-w-4xl mx-auto">
              <div className="aspect-video rounded-2xl overflow-hidden bg-muted relative">
                {!isPlaying && product.demoVideo.thumbnail ? (
                  <div 
                    className="relative w-full h-full cursor-pointer group"
                    onClick={() => setIsPlaying(true)}
                  >
                    <Image
                      src={product.demoVideo.thumbnail}
                      alt="视频预览"
                      fill
                      className="object-cover transition-transform group-hover:scale-105"
                      priority
                    />
                    {/* 播放按钮覆盖层 */}
                    <div className="absolute inset-0 flex items-center justify-center bg-black/30 group-hover:bg-black/40 transition-colors">
                      <div className="w-20 h-20 rounded-full bg-white/90 flex items-center justify-center group-hover:scale-110 transition-transform">
                        <Play className="w-10 h-10 text-primary fill-primary translate-x-0.5" />
                      </div>
                    </div>
                    {/* 可选：添加视频时长或标题 */}
                    <div className="absolute bottom-0 inset-x-0 bg-gradient-to-t from-black/60 to-transparent p-6">
                      <p className="text-white text-lg font-medium">
                        了解产品功能详解
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="w-full h-full">
                    <iframe
                      src={`${product.demoVideo.url}${isPlaying ? '?autoplay=1' : ''}`}
                      className="w-full h-full"
                      allow="autoplay; fullscreen"
                      allowFullScreen
                      title="产品演示视频"
                    />
                  </div>
                )}
              </div>
              {/* 视频下方的补充说明 */}
              <div className="mt-6 text-center">
                <p className="text-muted-foreground">
                  观看视频了解更多产品细节和使用场景
                </p>
              </div>
            </div>
          </div>
        )}

        {/* 详细功能列表部分 */}
        {product.featureList && product.featureList.length > 0 && (
          <div className="mt-24">
            <h3 className="text-2xl font-bold tracking-tight mb-8 text-center">功能详情</h3>
            <div className="space-y-24">
              {product.featureList.map((category, categoryIndex) => (
                <div key={categoryIndex}>
                  <div className="max-w-2xl mx-auto text-center mb-12">
                    <h4 className="text-xl font-semibold mb-4">{category.title}</h4>
                    {category.description && (
                      <p className="text-muted-foreground">{category.description}</p>
                    )}
                  </div>
                  <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
                    {category.features.map((feature, featureIndex) => (
                      <Card 
                        key={featureIndex} 
                        className="group hover:shadow-lg transition-all hover:-translate-y-1 dark:hover:bg-muted/5"
                      >
                        <CardContent className="p-6">
                          {feature.icon && (
                            <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4 group-hover:bg-primary/20 transition-colors">
                              {renderIcon(feature.icon)}
                            </div>
                          )}
                          <h5 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                            {feature.name}
                          </h5>
                          <p className="text-muted-foreground text-sm">{feature.description}</p>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 产品优势部分 */}
        {product.benefits && product.benefits.length > 0 && (
          <div className="mt-24">
            <h3 className="text-2xl font-bold tracking-tight mb-8 text-center">产品优势</h3>
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              {product.benefits.map((benefit, index) => (
                <Card key={index} className="group hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <h4 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                      {benefit.title}
                    </h4>
                    <p className="text-muted-foreground text-sm">{benefit.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* 联系按钮 */}
        <div className="mt-24 text-center">
          <Button asChild size="lg" className="px-8 py-6 text-lg hover:scale-105 transition-transform">
            <Link href="/contact-us">联系销售</Link>
          </Button>
        </div>
      </div>
    </div>
  )
}