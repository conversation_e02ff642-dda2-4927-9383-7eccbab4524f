'use client'

import { <PERSON><PERSON><PERSON>, CircleDot, Play, Sparkles, Zap, Brain, Cpu, GraduationCap } from 'lucide-react'
import React, { useState } from 'react'
import { Button } from '../ui/button'
import Link from 'next/link'
import { motion } from "framer-motion"

function HeroSection() {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false)

  const businessHighlights = [
    { icon: Brain, label: 'AI智能标注', color: 'text-blue-600' },
    { icon: Cpu, label: 'CPU算力租用', color: 'text-indigo-600' },
    { icon: GraduationCap, label: '教育培训管理', color: 'text-purple-600' }
  ]

  return (
    <div className="relative isolate min-h-screen flex items-start justify-center overflow-hidden">
      {/* 增强的背景层 */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50/40 to-indigo-50/20" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_40%_20%,rgba(59,130,246,0.15)_0%,transparent_50%),radial-gradient(circle_at_80%_0%,rgba(37,99,235,0.12)_0%,transparent_50%),radial-gradient(circle_at_0%_50%,rgba(29,78,216,0.08)_0%,transparent_50%)] opacity-90" />
        <div className="absolute inset-0 grid-bg opacity-25" />

        {/* 动态粒子效果 */}
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-blue-400/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -30, 0],
                opacity: [0.2, 0.8, 0.2],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
      </div>

      {/* 增强的浮动装饰元素 */}
      <div className="absolute top-20 left-20 w-80 h-80 rounded-full blur-3xl animate-float opacity-70" style={{ background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.12), rgba(37, 99, 235, 0.08))' }} />
      <div className="absolute bottom-20 right-20 w-96 h-96 rounded-full blur-3xl animate-float opacity-50" style={{ background: 'linear-gradient(135deg, rgba(99, 102, 241, 0.10), rgba(59, 130, 246, 0.06))', animationDelay: '2s' }} />
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full blur-3xl opacity-30 animate-float" style={{ background: 'radial-gradient(circle, rgba(59, 130, 246, 0.05) 0%, transparent 70%)', animationDelay: '4s' }} />

      <div className="relative z-10 mx-auto max-w-7xl px-6 lg:px-8 w-full py-20 sm:py-24 lg:py-32">
        <div className="mx-auto max-w-5xl text-center">
          {/* 增强的logo动画 */}
          <motion.div
            className="flex justify-center mb-12 pt-8 sm:pt-12 lg:pt-16"
            initial={{ opacity: 0, scale: 0.3, rotate: -10 }}
            animate={{ opacity: 1, scale: 1, rotate: 0 }}
            transition={{ duration: 1.2, ease: "easeOut" }}
          >
            <div className="relative pulse-ring">
              <div className="relative">
                <CircleDot className="h-24 w-24 animate-pulse-glow-modern drop-shadow-2xl" style={{ color: 'rgb(59 130 246)' }} />
                <div className="absolute inset-0 h-24 w-24 rounded-full blur-2xl animate-pulse" style={{ background: 'rgba(59, 130, 246, 0.3)' }} />

                {/* 环绕的小图标 */}
                {businessHighlights.map((item, index) => (
                  <motion.div
                    key={item.label}
                    className="absolute"
                    style={{
                      top: '50%',
                      left: '50%',
                      transform: `translate(-50%, -50%) rotate(${index * 120}deg) translateY(-60px)`,
                    }}
                    animate={{
                      rotate: [0, 360],
                    }}
                    transition={{
                      duration: 20,
                      repeat: Infinity,
                      ease: "linear",
                    }}
                  >
                    <div className={`p-2 rounded-full bg-white shadow-lg ${item.color}`}>
                      <item.icon className="h-4 w-4" />
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* 增强的标题 */}
          <motion.div
            className="mb-10"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.3, ease: "easeOut" }}
          >
            <div className="flex items-center justify-center gap-2 mb-8">
              <Sparkles className="h-6 w-6 text-blue-600 animate-pulse" />
              <span className="text-lg font-semibold text-blue-600">引领数字化未来</span>
              <Sparkles className="h-6 w-6 text-blue-600 animate-pulse" />
            </div>
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl text-gradient-modern">
              智能标注 · 算力租用 · 教育管理
            </h1>
          </motion.div>

          {/* 增强的描述 */}
          <motion.p
            className="text-lg leading-relaxed text-slate-600 max-w-3xl mx-auto mb-10"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5, ease: "easeOut" }}
          >
            专业的AI数据标注服务、灵活的云计算资源租用、完整的教育培训管理解决方案。
            <br />
            <span className="text-blue-600 font-medium">助力企业数字化转型，推动人工智能产业发展</span>
          </motion.p>

          {/* 业务亮点 */}
          <motion.div
            className="flex flex-wrap items-center justify-center gap-6 mb-10"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
          >
            {businessHighlights.map((item, index) => (
              <motion.div
                key={item.label}
                className="flex items-center gap-3 px-4 py-2 rounded-full bg-white/60 backdrop-blur-sm border border-white/20 shadow-lg hover-lift"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <item.icon className={`h-5 w-5 ${item.color}`} />
                <span className="text-sm font-medium text-slate-700">{item.label}</span>
              </motion.div>
            ))}
          </motion.div>

          {/* 增强的CTA按钮 */}
          <motion.div
            className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-12"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8, ease: "easeOut" }}
          >
            <Button asChild className="btn-modern shadow-button-modern group px-8 py-4 text-lg">
              <Link href="/products">
                <Zap className="mr-2 h-5 w-5" />
                探索产品
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>

            <Button
              className="glass px-8 py-4 text-lg h-12 bg-white/10 backdrop-blur-sm border hover:bg-white/20 rounded-xl transition-all duration-300 hover-lift group"
              style={{ borderColor: 'rgba(59, 130, 246, 0.2)', color: 'rgb(59 130 246)' }}
              onClick={() => setIsVideoPlaying(true)}
            >
              <Play className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform duration-200" />
              观看演示
            </Button>
          </motion.div>

          {/* 统计数据 */}
          <motion.div
            className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1, ease: "easeOut" }}
          >
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">500+</div>
              <div className="text-sm text-slate-600">服务企业</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">99.5%</div>
              <div className="text-sm text-slate-600">服务满意度</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">24/7</div>
              <div className="text-sm text-slate-600">技术支持</div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* 视频模态框 */}
      {isVideoPlaying && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={() => setIsVideoPlaying(false)}
        >
          <motion.div
            className="relative w-full max-w-4xl mx-4 aspect-video bg-slate-900 rounded-2xl overflow-hidden shadow-2xl"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-white text-center">
                <Play className="h-16 w-16 mx-auto mb-4 opacity-60" />
                <p className="text-lg opacity-80">演示视频即将推出</p>
              </div>
            </div>
            <button
              className="absolute top-4 right-4 text-white/60 hover:text-white transition-colors duration-200"
              onClick={() => setIsVideoPlaying(false)}
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </motion.div>
        </motion.div>
      )}
    </div>
  )
}

export default HeroSection