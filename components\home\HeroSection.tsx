'use client'

import { ArrowR<PERSON>, CircleDot } from 'lucide-react'
import React from 'react'
import { Button } from '../ui/button'
import Link from 'next/link'
import { motion } from "framer-motion"

function HeroSection() {
  return (
    <div className="relative isolate min-h-screen flex items-center">
      {/* 现代化背景层 */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50/40 to-indigo-50/20" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_40%_20%,rgba(59,130,246,0.12)_0%,transparent_50%),radial-gradient(circle_at_80%_0%,rgba(37,99,235,0.08)_0%,transparent_50%),radial-gradient(circle_at_0%_50%,rgba(29,78,216,0.06)_0%,transparent_50%)] opacity-80" />
        <div className="absolute inset-0 grid-bg opacity-20" />
      </div>

      {/* 浮动装饰元素 */}
      <div className="absolute top-20 left-20 w-72 h-72 rounded-full blur-3xl animate-float opacity-60" style={{ background: 'rgba(59, 130, 246, 0.08)' }} />
      <div className="absolute bottom-20 right-20 w-96 h-96 rounded-full blur-3xl animate-float opacity-40" style={{ background: 'rgba(37, 99, 235, 0.06)', animationDelay: '2s' }} />

      <div className="relative z-10 mx-auto max-w-7xl px-6 lg:px-8 w-full">
        <div className="mx-auto max-w-4xl text-center">
          <motion.div
            className="flex justify-center mb-12"
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            <div className="relative pulse-ring">
              <CircleDot className="h-20 w-20 animate-pulse-glow-modern drop-shadow-lg" style={{ color: 'rgb(59 130 246)' }} />
              <div className="absolute inset-0 h-20 w-20 rounded-full blur-xl animate-pulse" style={{ background: 'rgba(59, 130, 246, 0.2)' }} />
            </div>
          </motion.div>

          <motion.h1
            className="text-5xl font-bold tracking-tight sm:text-7xl lg:text-8xl text-gradient-modern mb-8"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
          >
            智能标注 · 算力租用 · 教育管理
          </motion.h1>

          <motion.p
            className="mt-8 text-xl leading-relaxed text-slate-600 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5, ease: "easeOut" }}
          >
            专业的AI数据标注服务、灵活的云计算资源租用、完整的教育培训管理解决方案。助力企业数字化转型，推动人工智能产业发展。
          </motion.p>

          <motion.div
            className="mt-12 flex flex-col sm:flex-row items-center justify-center gap-6"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7, ease: "easeOut" }}
          >
            <Button asChild className="btn-modern shadow-button-modern group">
              <Link href="/products">
                探索产品
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
            <Button className="glass px-8 py-4 text-lg h-12 bg-white/10 backdrop-blur-sm border hover:bg-white/20 rounded-xl transition-all duration-300 hover-lift" style={{ borderColor: 'rgba(59, 130, 246, 0.2)', color: 'rgb(59 130 246)' }}>
              了解更多
            </Button>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

export default HeroSection